-- 查询结果去重示例
USE [CAMDB]
GO

-- 方法1: 使用 DISTINCT 去重
-- 适用于完全重复的记录
DECLARE @DataDate bigint = 20250803

SELECT DISTINCT 
    t.城市名称,
    t.是否三区城市,
    t.日期,
    t.餐饮订单量,
    t.餐饮实付交易额,
    ISNULL(p.拼好饭订单量, 0) AS 拼好饭订单量,
    t.餐饮订单量 - ISNULL(p.拼好饭订单量, 0) AS 剔除PHF后餐饮订单量
FROM (
    SELECT 
        CityName AS 城市名称,
        CASE WHEN CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') 
             THEN 1 ELSE 0 END AS 是否三区城市,
        DataDate AS 日期,
        SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END) AS 餐饮订单量,
        SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) AS 餐饮实付交易额
    FROM tbCateringRealCollect_New
    WHERE DataYear = 2025 
        AND DataDate >= @DataDate
        AND CityName NOT IN ('廉江市','阳西')
    GROUP BY CityName, DataDate
) t
LEFT JOIN (
    SELECT 
        CityName AS 城市名称,
        DataDate AS 日期,
        SUM(PHFOrderCnt) AS 拼好饭订单量 
    FROM tbPHFOrderCnt
    WHERE DataYear = 2025 
        AND CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
    GROUP BY CityName, DataDate
) p ON t.城市名称 = p.城市名称 AND t.日期 = p.日期

-- 方法2: 使用 ROW_NUMBER() 窗口函数去重
-- 适用于根据特定字段组合去重，保留最新或最优记录
WITH DeduplicatedData AS (
    SELECT 
        t.*,
        ISNULL(p.拼好饭订单量, 0) AS 拼好饭订单量,
        t.餐饮订单量 - ISNULL(p.拼好饭订单量, 0) AS 剔除PHF后餐饮订单量,
        ROW_NUMBER() OVER (
            PARTITION BY t.城市名称, t.日期 
            ORDER BY t.餐饮实付交易额 DESC  -- 按实付交易额降序，保留最大值
        ) AS rn
    FROM (
        SELECT 
            CityName AS 城市名称,
            CASE WHEN CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') 
                 THEN 1 ELSE 0 END AS 是否三区城市,
            DataDate AS 日期,
            SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END) AS 餐饮订单量,
            SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) AS 餐饮实付交易额
        FROM tbCateringRealCollect_New
        WHERE DataYear = 2025 
            AND DataDate >= @DataDate
            AND CityName NOT IN ('廉江市','阳西')
        GROUP BY CityName, DataDate
    ) t
    LEFT JOIN (
        SELECT 
            CityName AS 城市名称,
            DataDate AS 日期,
            SUM(PHFOrderCnt) AS 拼好饭订单量 
        FROM tbPHFOrderCnt
        WHERE DataYear = 2025 
            AND CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
        GROUP BY CityName, DataDate
    ) p ON t.城市名称 = p.城市名称 AND t.日期 = p.日期
)
SELECT 
    城市名称,
    是否三区城市,
    日期,
    餐饮订单量,
    餐饮实付交易额,
    拼好饭订单量,
    剔除PHF后餐饮订单量
FROM DeduplicatedData
WHERE rn = 1  -- 只保留每个城市每天的第一条记录
ORDER BY 日期, 城市名称

-- 方法3: 使用 GROUP BY 聚合去重
-- 适用于需要对重复数据进行聚合处理
SELECT 
    城市名称,
    是否三区城市,
    日期,
    SUM(餐饮订单量) AS 餐饮订单量,
    SUM(餐饮实付交易额) AS 餐饮实付交易额,
    SUM(拼好饭订单量) AS 拼好饭订单量,
    SUM(餐饮订单量) - SUM(拼好饭订单量) AS 剔除PHF后餐饮订单量
FROM (
    -- 原始查询结果
    SELECT 
        t.城市名称,
        t.是否三区城市,
        t.日期,
        t.餐饮订单量,
        t.餐饮实付交易额,
        ISNULL(p.拼好饭订单量, 0) AS 拼好饭订单量
    FROM (
        SELECT 
            CityName AS 城市名称,
            CASE WHEN CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') 
                 THEN 1 ELSE 0 END AS 是否三区城市,
            DataDate AS 日期,
            SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END) AS 餐饮订单量,
            SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) AS 餐饮实付交易额
        FROM tbCateringRealCollect_New
        WHERE DataYear = 2025 
            AND DataDate >= @DataDate
            AND CityName NOT IN ('廉江市','阳西')
        GROUP BY CityName, DataDate
    ) t
    LEFT JOIN (
        SELECT 
            CityName AS 城市名称,
            DataDate AS 日期,
            SUM(PHFOrderCnt) AS 拼好饭订单量 
        FROM tbPHFOrderCnt
        WHERE DataYear = 2025 
            AND CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
        GROUP BY CityName, DataDate
    ) p ON t.城市名称 = p.城市名称 AND t.日期 = p.日期
) AS SubQuery
GROUP BY 城市名称, 是否三区城市, 日期
ORDER BY 日期, 城市名称

-- 方法4: 使用 EXISTS 去重
-- 适用于复杂的去重条件
SELECT DISTINCT 
    t1.城市名称,
    t1.是否三区城市,
    t1.日期,
    t1.餐饮订单量,
    t1.餐饮实付交易额,
    t1.拼好饭订单量,
    t1.剔除PHF后餐饮订单量
FROM (
    -- 原始查询
    SELECT 
        t.城市名称,
        t.是否三区城市,
        t.日期,
        t.餐饮订单量,
        t.餐饮实付交易额,
        ISNULL(p.拼好饭订单量, 0) AS 拼好饭订单量,
        t.餐饮订单量 - ISNULL(p.拼好饭订单量, 0) AS 剔除PHF后餐饮订单量
    FROM (
        SELECT 
            CityName AS 城市名称,
            CASE WHEN CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') 
                 THEN 1 ELSE 0 END AS 是否三区城市,
            DataDate AS 日期,
            SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END) AS 餐饮订单量,
            SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) AS 餐饮实付交易额
        FROM tbCateringRealCollect_New
        WHERE DataYear = 2025 
            AND DataDate >= @DataDate
            AND CityName NOT IN ('廉江市','阳西')
        GROUP BY CityName, DataDate
    ) t
    LEFT JOIN (
        SELECT 
            CityName AS 城市名称,
            DataDate AS 日期,
            SUM(PHFOrderCnt) AS 拼好饭订单量 
        FROM tbPHFOrderCnt
        WHERE DataYear = 2025 
            AND CityName IN ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
        GROUP BY CityName, DataDate
    ) p ON t.城市名称 = p.城市名称 AND t.日期 = p.日期
) t1
WHERE NOT EXISTS (
    -- 检查是否存在相同的记录但数据更优
    SELECT 1 FROM (
        -- 重复相同的查询逻辑
        SELECT 
            t.城市名称,
            t.日期,
            t.餐饮实付交易额
        FROM (
            SELECT 
                CityName AS 城市名称,
                DataDate AS 日期,
                SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) AS 餐饮实付交易额
            FROM tbCateringRealCollect_New
            WHERE DataYear = 2025 
                AND DataDate >= @DataDate
                AND CityName NOT IN ('廉江市','阳西')
            GROUP BY CityName, DataDate
        ) t
    ) t2
    WHERE t2.城市名称 = t1.城市名称 
        AND t2.日期 = t1.日期 
        AND t2.餐饮实付交易额 > t1.餐饮实付交易额
)
ORDER BY t1.日期, t1.城市名称
